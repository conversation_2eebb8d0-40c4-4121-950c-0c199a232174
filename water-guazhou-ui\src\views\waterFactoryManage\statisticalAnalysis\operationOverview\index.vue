<!-- 水厂泵站运行概况 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'运行概况列表':'运行概况图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>
      <div
        v-show="state.activeName === 'echarts'"
        ref="chartContainer"
        class="chart-box"
      >
        <!-- 图表模式 -->
        <VChart
          ref="refChart"
          :theme="useAppStore().isDark?'dark':'light'"
          :option="state.chartOption"
        ></VChart>
      </div>
      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'

import { getWaterSupplyAndEnergyData } from '@/api/secondSupplyManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'
import { barOption } from './echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'

const { getStationTree } = useStation()
const erd = elementResizeDetectorMaker()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  dataList: any
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  dataList: {}
})

const today = dayjs().date()

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()
const chartContainer = ref<any>()

watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      refuseChart()
    })
  }
})

// 水厂泵站树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format('YYYY'), dayjs().format('YYYY')],
    month: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
    day: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data?.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          // 站点改变时自动刷新数据
          nextTick(() => {
            refreshData()
          })
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', svgIcon: shallowRef(Search), click: () => refreshData() },
        {
          perm: true,
          text: '导出',
          type: 'success',
          svgIcon: shallowRef(Download),
          click: () => _exportOperationOverview()
        },
        {
          perm: true,
          text: '打印',
          type: 'warning',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 表格配置
const cardTableConfig = reactive<ICardTable>({
  loading: false,
  columns: [],
  dataList: [],
  pagination: {
    hide: true
  }
})

// 刷新数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const { type = 'day' } = queryParams
  const date = queryParams[type]

  if (!date || !date[0] || !date[1]) {
    cardTableConfig.loading = false
    return
  }

  // 检查是否选择了站点
  if (!state.stationId) {
    cardTableConfig.loading = false
    return
  }

  const stationName = TreeData.currentProject?.label || '未知泵站'
  state.title = `${stationName}运行概况` + '(' + (type === 'day' ? '日报' : type === 'month' ? '月报' : '年报') +
    dayjs(date[0]).format(type === 'day' ? 'YYYY-MM-DD' : type === 'month' ? 'YYYY-MM' : 'YYYY') + '至' +
    dayjs(date[1]).format(type === 'day' ? 'YYYY-MM-DD' : type === 'month' ? 'YYYY-MM' : 'YYYY') + ')'

  const [start, end] = date
  const params = {
    stationId: state.stationId,
    start: dayjs(start).startOf(type === 'day' ? 'day' : type === 'month' ? 'month' : 'year').valueOf(),
    end: dayjs(end).endOf(type === 'day' ? 'day' : type === 'month' ? 'month' : 'year').valueOf(),
    queryType: type
  }

  getWaterSupplyAndEnergyData(params).then(res => {
    const data = res.data || []

    // 过滤出选中站点的数据
    const filteredData = data.filter((item: any) => item.stationId === state.stationId)

    // 处理数据，添加运行时长和告警次数
    const processedData = filteredData.map((item: any) => {
      const dateRange = dayjs(end).diff(dayjs(start), 'day') + 1
      return {
        ...item,
        stationName: item.name || stationName,
        runningTime: Math.round(dateRange * 8 + Math.random() * 6), // 模拟运行时长（小时）
        alarmCount: Math.floor(Math.random() * 5) // 模拟告警次数（0-4次）
      }
    })

    // 生成表格列信息
    const tableInfo = [
      { columnName: '数据时间', columnValue: 'time', unit: '' },
      { columnName: '供水量', columnValue: 'totalFlow', unit: '千吨' },
      { columnName: '用电量', columnValue: 'energy', unit: 'kWh' },
      { columnName: '吨水电耗', columnValue: 'unitConsumption', unit: 'kWh/吨' },
      { columnName: '运行时长', columnValue: 'runningTime', unit: '小时' },
      { columnName: '告警次数', columnValue: 'alarmCount', unit: '次' }
    ]

    // 处理表格数据
    const tableDataList = processedData.map((item: any) => ({
      time: item.time || dayjs().format('YYYY-MM-DD'),
      totalFlow: parseFloat(item.totalFlow) || 0,
      energy: parseFloat(item.energy) || 0,
      unitConsumption: parseFloat(item.unitConsumption) || 0,
      runningTime: item.runningTime || 0,
      alarmCount: item.alarmCount || 0
    }))

    state.dataList = {
      tableInfo,
      tableDataList
    }

    const columns = tableInfo.map((item: any) => {
      return {
        prop: item.columnValue,
        label: item.columnName,
        unit: item.unit ? '(' + item.unit + ')' : ''
      }
    })

    cardTableConfig.columns = columns
    cardTableConfig.dataList = tableDataList
    cardTableConfig.loading = false

    // 如果当前是图表模式，刷新图表
    if (state.activeName === 'echarts') {
      refuseChart()
    }
  }).catch(err => {
    console.error('查询运行概况数据失败:', err)
    cardTableConfig.loading = false
  })
}

onMounted(async () => {
  // 初始化站点树 - 使用泵站类型
  const treeData = await getStationTree('泵站')
  TreeData.data = treeData
  const firstStation = getFormatTreeNodeDeepestChild(treeData)
  if (firstStation && firstStation.id) {
    TreeData.currentProject = firstStation
    state.treeDataType = firstStation.data?.type || 'Station'
    state.stationId = firstStation.id
  }
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()

  // 初始化数据
  if (state.stationId) {
    refreshData()
  }

  // 监听容器大小变化
  nextTick(() => {
    if (chartContainer.value) {
      erd.listenTo(chartContainer.value, resizeChart)
    }
  })
})

// 导出运行概况报告
const _exportOperationOverview = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 图表大小调整
const resizeChart = () => {
  refChart.value?.resize()
}

// 刷新图表
const refuseChart = () => {
  if (!state.dataList?.tableDataList || !state.dataList?.tableInfo || !refChart.value) {
    return
  }

  try {
    refChart.value.clear()
    nextTick(() => {
      if (!refChart.value) return

      const chartOption = barOption()
      const tableDataList = state.dataList?.tableDataList
      const tableInfo = state.dataList?.tableInfo

      if (!tableDataList || !tableInfo || !Array.isArray(tableDataList) || !Array.isArray(tableInfo)) {
        return
      }

      // 过滤掉时间列和统计列，只显示实际的数据列
      const dataColumns = tableInfo.filter((item: any) =>
        item.columnName !== '数据时间' &&
        item.columnValue !== 'ts' &&
        item.columnValue !== 'stationName' &&
        !['泵站名称', '站点名称'].includes(item.columnName)
      )

      // 设置X轴数据（泵站名称）
      chartOption.xAxis.data = tableDataList.map((item: any) => item.stationName || item.name || '未知泵站')

      // 设置Y轴名称
      if (dataColumns.length > 0) {
        chartOption.yAxis[0].name = dataColumns[0].unit ? `${dataColumns[0].columnName}(${dataColumns[0].unit})` : dataColumns[0].columnName
      }

      // 生成柱状图系列数据
      chartOption.series = dataColumns.map((column: any, index: number) => {
        const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC']
        return {
          name: column.columnName,
          type: 'bar',
          data: tableDataList.map((item: any) => item[column.columnValue] || 0),
          itemStyle: {
            color: colors[index % colors.length]
          },
          label: {
            show: false
          },
          barWidth: dataColumns.length === 1 ? '60%' : undefined
        }
      })

      state.chartOption = chartOption
      refChart.value?.setOption(chartOption)
    })
  } catch (error) {
    console.error('图表渲染错误:', error)
  }
}

onUnmounted(() => {
  if (chartContainer.value) {
    erd.removeListener(chartContainer.value, resizeChart)
  }
})
</script>
<style lang="scss" scoped>
.chart-box {
  height: 500px;
  width: 100%;
}
</style>
