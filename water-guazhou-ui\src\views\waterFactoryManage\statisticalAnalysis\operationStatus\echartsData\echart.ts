// 泵组运行状态横向柱状图配置
export function horizontalBarOption() {
  const option = {
    title: {
      text: '',
      textStyle: {
        color: '#5470C6',
        fontSize: '14px'
      },
      top: 10
    },
    grid: {
      left: 120,
      right: 50,
      top: 50,
      bottom: 50
    },
    legend: {
      top: 20,
      type: 'scroll',
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        let result = params[0].name + '<br/>'
        params.forEach((param: any) => {
          result += param.marker + param.seriesName + ': ' + param.value + '<br/>'
        })
        return result
      }
    },
    xAxis: {
      type: 'value',
      name: '单位：小时',
      nameLocation: 'end',
      nameTextStyle: {
        fontSize: 12,
        color: '#666'
      },
      axisLabel: {
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    },
    yAxis: {
      type: 'category',
      data: [] as string[],
      axisLabel: {
        fontSize: 11,
        margin: 8
      },
      axisTick: {
        alignWithLabel: true
      },
      inverse: true // 反转Y轴，使第一个数据在顶部
    },
    series: [] as any[]
  }
  return option
}
