package org.thingsboard.server.dao.sql.waterMonitoring;

import com.influxdb.query.FluxTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.waterMonitoring.PumpStationService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 泵站监控服务实现类
 */
@Slf4j
@Service
@SqlDao
public class PumpStationServiceImpl implements PumpStationService {

    @Autowired
    private PumpStationMapper pumpStationMapper;

    @Autowired
    private InfluxService influxService;

    // 使用现有的站点服务获取泵站列表

    @Override
    public Map<String, Object> getMonitorData(List<String> stationIds, String timeGranularity, Long startTime, Long endTime, String pumpType, String tenantId) {
        Map<String, Object> result = new HashMap<>();

        // 如果没有指定泵站ID，则获取所有泵站
        if (stationIds == null || stationIds.isEmpty()) {
            List<Map<String, Object>> stations = getStationList(tenantId);
            stationIds = stations.stream()
                    .map(station -> (String) station.get("id"))
                    .collect(Collectors.toList());
        }

        // 如果没有指定时间范围，默认查询最近24小时
        if (startTime == null) {
            startTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000;
        }
        if (endTime == null) {
            endTime = System.currentTimeMillis();
        }

        // 构建查询公式列表
        List<String> pumpWaterFormulas = new ArrayList<>();
        List<String> otherWaterFormulas = new ArrayList<>();
        List<String> processWaterFormulas = new ArrayList<>();
        List<String> intermediateWaterFormulas = new ArrayList<>();
        List<String> energyConsumptionFormulas = new ArrayList<>();

        for (String stationId : stationIds) {
            // 根据泵站类型构建不同的查询公式
            if ("electric".equals(pumpType)) {
                pumpWaterFormulas.add(stationId + ".pumpWater");
                otherWaterFormulas.add(stationId + ".otherWater");
            } else {
                processWaterFormulas.add(stationId + ".processWater");
                intermediateWaterFormulas.add(stationId + ".intermediateWater");
            }

            // 能耗数据
            energyConsumptionFormulas.add(stationId + ".energyConsumption");
        }

        // 查询泵水量与其他量数据
        List<Map<String, Object>> pumpWaterData = new ArrayList<>();
        if (!pumpWaterFormulas.isEmpty()) {
            List<FluxTable> pumpWaterTables = influxService.findData(pumpWaterFormulas, startTime, endTime, timeGranularity);
            List<FluxTable> otherWaterTables = influxService.findData(otherWaterFormulas, startTime, endTime, timeGranularity);

            // 处理查询结果
            pumpWaterData = processPumpWaterData(pumpWaterTables, otherWaterTables, stationIds);
        }

        // 查询进行中水与中间水量数据
        List<Map<String, Object>> processWaterData = new ArrayList<>();
        if (!processWaterFormulas.isEmpty()) {
            List<FluxTable> processWaterTables = influxService.findData(processWaterFormulas, startTime, endTime, timeGranularity);
            List<FluxTable> intermediateWaterTables = influxService.findData(intermediateWaterFormulas, startTime, endTime, timeGranularity);

            // 处理查询结果
            processWaterData = processProcessWaterData(processWaterTables, intermediateWaterTables, stationIds);
        }

        // 查询能耗趋势数据
        List<Map<String, Object>> trendData = new ArrayList<>();
        if (!energyConsumptionFormulas.isEmpty()) {
            List<FluxTable> energyConsumptionTables = influxService.findData(energyConsumptionFormulas, startTime, endTime, timeGranularity);

            // 处理查询结果
            trendData = processTrendData(energyConsumptionTables, stationIds);
        }

        result.put("pumpWaterData", pumpWaterData);
        result.put("processWaterData", processWaterData);
        result.put("trendData", trendData);

        return result;
    }

    @Override
    public List<Map<String, Object>> getStationDetail(List<String> stationIds, String tenantId) {
        // 如果没有指定泵站ID，则获取所有泵站
        if (stationIds == null || stationIds.isEmpty()) {
            List<Map<String, Object>> stations = getStationList(tenantId);
            stationIds = stations.stream()
                    .map(station -> (String) station.get("id"))
                    .collect(Collectors.toList());
        }

        // 获取泵站详情数据
        List<Map<String, Object>> pumpDetails = pumpStationMapper.getStationDetail(stationIds, tenantId);

        // 按泵站ID分组
        Map<String, List<Map<String, Object>>> pumpsByStation = pumpDetails.stream()
                .collect(Collectors.groupingBy(detail -> (String) detail.get("stationId")));

        // 构建结果
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : pumpsByStation.entrySet()) {
            String stationId = entry.getKey();
            List<Map<String, Object>> pumps = entry.getValue();

            if (!pumps.isEmpty()) {
                Map<String, Object> stationDetail = new HashMap<>();
                stationDetail.put("stationId", stationId);
                stationDetail.put("stationName", pumps.get(0).get("stationName"));
                stationDetail.put("pumps", pumps);
                result.add(stationDetail);
            }
        }

        return result;
    }

    @Override
    public void applyScheme(String schemeId, List<String> stationIds, String tenantId) {
        // 应用方案逻辑
        pumpStationMapper.applyScheme(schemeId, stationIds, tenantId);
    }

    /**
     * 获取泵站列表
     */
    private List<Map<String, Object>> getStationList(String tenantId) {
        return pumpStationMapper.getStationList(tenantId);
    }

    /**
     * 处理泵水量与其他量数据
     */
    private List<Map<String, Object>> processPumpWaterData(List<FluxTable> pumpWaterTables, List<FluxTable> otherWaterTables, List<String> stationIds) {
        // 实际实现中需要根据InfluxDB返回的数据结构进行处理
        // 这里提供一个示例实现
        List<Map<String, Object>> result = new ArrayList<>();

        // 模拟数据
        for (int i = 0; i < stationIds.size(); i++) {
            Map<String, Object> stationData = new HashMap<>();
            stationData.put("stationId", stationIds.get(i));
            stationData.put("stationName", "泵站" + (i + 1));
            stationData.put("pumpWater", 200 + Math.random() * 50);
            stationData.put("otherWater", 150 + Math.random() * 50);
            result.add(stationData);
        }

        return result;
    }

    /**
     * 处理进行中水与中间水量数据
     */
    private List<Map<String, Object>> processProcessWaterData(List<FluxTable> processWaterTables, List<FluxTable> intermediateWaterTables, List<String> stationIds) {
        // 实际实现中需要根据InfluxDB返回的数据结构进行处理
        // 这里提供一个示例实现
        List<Map<String, Object>> result = new ArrayList<>();

        // 模拟数据
        for (int i = 0; i < stationIds.size(); i++) {
            Map<String, Object> stationData = new HashMap<>();
            stationData.put("stationId", stationIds.get(i));
            stationData.put("stationName", "泵站" + (i + 1));
            stationData.put("processWater", 180 + Math.random() * 50);
            stationData.put("intermediateWater", 130 + Math.random() * 50);
            result.add(stationData);
        }

        return result;
    }

    /**
     * 处理能耗趋势数据
     */
    private List<Map<String, Object>> processTrendData(List<FluxTable> energyConsumptionTables, List<String> stationIds) {
        // 实际实现中需要根据InfluxDB返回的数据结构进行处理
        // 这里提供一个示例实现
        List<Map<String, Object>> result = new ArrayList<>();

        // 模拟数据
        String[] timePoints = {"3小时", "6小时", "9小时", "12小时", "15小时", "18小时", "21小时", "24小时"};

        for (int i = 0; i < stationIds.size(); i++) {
            Map<String, Object> stationData = new HashMap<>();
            stationData.put("stationId", stationIds.get(i));
            stationData.put("stationName", "泵站" + (i + 1));

            List<Map<String, Object>> trends = new ArrayList<>();
            for (String timePoint : timePoints) {
                Map<String, Object> point = new HashMap<>();
                point.put("time", timePoint);
                // 根据泵站ID生成不同的趋势数据
                double baseValue = 100 + (i * 20);
                double randomFactor = Math.random() * 50;

                // 为泵站5生成特殊的下降趋势
                if (i == 4 && (timePoint.equals("21小时") || timePoint.equals("24小时"))) {
                    point.put("value", 0); // 最后两个时间点为0
                } else {
                    point.put("value", baseValue + randomFactor);
                }

                trends.add(point);
            }

            stationData.put("trends", trends);
            result.add(stationData);
        }

        return result;
    }
}
