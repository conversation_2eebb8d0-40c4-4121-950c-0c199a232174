import request from '@/plugins/axios'

// 获取泵站列表 - 使用现有的站点列表接口
export function getPumpStationList(params: any) {
  return request({
    url: '/api/station/list',
    method: 'get',
    params: {
      page: 1,
      size: 1000,
      type: params.type || '泵房'  // 指定泵房类型
    }
  })
}

// 获取泵站监控数据
export function getPumpStationMonitorData(params: {
  stationIds: string[];
  timeGranularity: string;
  startTime: number;
  endTime: number;
  pumpType?: string;
}) {
  // 将数组转换为逗号分隔的字符串
  const modifiedParams = {
    ...params,
    stationIds: params.stationIds.join(',')
  };

  return request({
    url: '/istar/api/boosterPumpStation/monitorData',
    method: 'get',
    params: modifiedParams
  })
}

// 获取泵站运行详情
export function getPumpStationDetail(params: {
  stationIds: string[];
}) {
  // 将数组转换为逗号分隔的字符串
  const modifiedParams = {
    stationIds: params.stationIds.join(',')
  };

  return request({
    url: '/istar/api/boosterPumpStation/detail',
    method: 'get',
    params: modifiedParams
  })
}

// 应用方案
export function applyPumpStationScheme(data: {
  schemeId: string;
  stationIds: string[];
}) {
  return request({
    url: '/istar/api/boosterPumpStation/applyScheme',
    method: 'post',
    data
  })
}
