<!-- 吨水电耗分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'吨水电耗分析':'吨水电耗图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>

      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'" class="content-container">
        <div class="list-layout">
          <!-- 左侧表格区域 -->
          <div class="table-container">
            <CardTable
              id="print"
              ref="refTable"
              class="card-table"
              :config="cardTableConfig"
            />
          </div>

          <!-- 右侧日期选择区域 -->
          <div class="date-selector">
            <div class="selector-title">选择日期</div>
            <div class="date-buttons">
              <el-button
                v-for="date in state.availableDates"
                :key="date"
                :type="state.selectedDate === date ? 'primary' : 'default'"
                size="small"
                @click="selectDate(date)"
                class="date-btn"
              >
                {{ formatDateButton(date) }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表模式 -->
      <div v-show="state.activeName === 'echarts'" class="content-container">
        <div class="chart-layout">
          <!-- 左侧图表区域 -->
          <div class="chart-container">
            <VChart
              ref="refChart"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.chartOption"
              class="line-chart"
            ></VChart>
          </div>

          <!-- 右侧日期选择区域 -->
          <div class="date-selector">
            <div class="selector-title">选择日期</div>
            <div class="date-buttons">
              <el-button
                v-for="date in state.availableDates"
                :key="date"
                :type="state.selectedDate === date ? 'primary' : 'default'"
                size="small"
                @click="selectDate(date)"
                class="date-btn"
              >
                {{ formatDateButton(date) }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ElMessageBox } from 'element-plus'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyAndEnergyDataDetail } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'

import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  availableDates: string[];
  selectedDate: string;
  chartData: any;
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  availableDates: [],
  selectedDate: '',
  chartData: null
})

const today = dayjs().date()

const refTable = ref()
const cardSearch = ref()
const refChart = ref<IECharts>()

// 监听模式切换
watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      setTimeout(() => {
        generateLineChart()
      }, 100) // 延迟确保DOM渲染完成
    })
  }
})

// 监听报表类型变化，重新获取数据
watch(() => cardSearch.value?.queryParams?.type, (newType) => {
  if (newType && TreeData.currentProject?.id) {
    nextTick(() => {
      refreshData()
    })
  }
})

// 水厂站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        if (val && val.id) {
          TreeData.currentProject = val
          state.treeDataType = val.data?.type || 'Station'
          state.stationId = val.id as string
          // 站点改变时自动刷新数据
          nextTick(() => {
            refreshData()
          })
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      hidden: true,
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, _query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, _query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, _query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportReport()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 获取水厂站点树数据
const getTreeData = async () => {
  const res = await getStationTree('水厂')
  TreeData.data = res
  const firstChild = getFormatTreeNodeDeepestChild(res)
  if (firstChild) {
    TreeData.currentProject = firstChild
    state.treeDataType = firstChild.data?.type || 'Station'
    state.stationId = firstChild.id as string
    cardSearchConfig.defaultParams.treeData = firstChild.id
  }
}

// 刷新数据
const refreshData = async () => {
  if (!state.stationId) {
    SLMessage.warning('请先选择站点')
    return
  }

  cardTableConfig.loading = true
  const params = cardSearch.value?.queryParams
  if (!params) {
    cardTableConfig.loading = false
    return
  }

  try {
    const res = await getWaterSupplyAndEnergyDataDetail({
      stationId: state.stationId,
      type: params.type,
      startTime: params[params.type][0],
      endTime: params[params.type][1]
    })

    if (res.data) {
      state.chartData = res.data
      processTableData(res.data)
      generateAvailableDates(res.data)

      // 默认选择第一个日期
      if (state.availableDates.length > 0) {
        state.selectedDate = state.availableDates[0]
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    SLMessage.error('获取数据失败')
  } finally {
    cardTableConfig.loading = false
  }
}

// 处理表格数据
const processTableData = (data: any) => {
  if (!data || !data.list) return

  // 生成列配置
  const columns = [
    { prop: 'name', label: '站点名称', minWidth: 120 },
    { prop: 'date', label: '日期', minWidth: 100 },
    { prop: 'waterFlow', label: '供水量(m³)', minWidth: 120 },
    { prop: 'powerConsumption', label: '电耗(kWh)', minWidth: 120 },
    { prop: 'tonWaterPower', label: '吨水电耗(kWh/m³)', minWidth: 140 }
  ]

  cardTableConfig.columns = columns
  cardTableConfig.dataList = data.list || []
}

// 生成可用日期列表
const generateAvailableDates = (data: any) => {
  if (!data || !data.list) return

  const dates = [...new Set(data.list.map(item => item.date))].sort()
  state.availableDates = dates
}

// 选择日期
const selectDate = (date: string) => {
  state.selectedDate = date
  if (state.activeName === 'echarts') {
    generateLineChart()
  }
}

// 格式化日期按钮显示
const formatDateButton = (date: string) => {
  return dayjs(date).format('MM-DD')
}

// 生成折线图
const generateLineChart = () => {
  if (!state.chartData || !state.selectedDate) return

  const selectedData = state.chartData.list.filter(item => item.date === state.selectedDate)

  if (selectedData.length === 0) return

  const option = {
    title: {
      text: `${state.selectedDate} 吨水电耗分析`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['供水量', '电耗', '吨水电耗'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: selectedData.map(item => item.name)
    },
    yAxis: [
      {
        type: 'value',
        name: '供水量(m³)/电耗(kWh)',
        position: 'left'
      },
      {
        type: 'value',
        name: '吨水电耗(kWh/m³)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '供水量',
        type: 'bar',
        data: selectedData.map(item => item.waterFlow),
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '电耗',
        type: 'bar',
        data: selectedData.map(item => item.powerConsumption),
        itemStyle: {
          color: '#91cc75'
        }
      },
      {
        name: '吨水电耗',
        type: 'line',
        yAxisIndex: 1,
        data: selectedData.map(item => item.tonWaterPower),
        itemStyle: {
          color: '#fac858'
        },
        lineStyle: {
          width: 3
        }
      }
    ]
  }

  state.chartOption = option

  nextTick(() => {
    refChart.value?.resize()
  })
}

// 导出报表
const _exportReport = async () => {
  if (!cardTableConfig.dataList.length) {
    SLMessage.warning('暂无数据可导出')
    return
  }

  try {
    await ElMessageBox.confirm('确定要导出当前数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里可以添加具体的导出逻辑
    SLMessage.success('导出成功')
  } catch {
    // 用户取消
  }
}

// 打印报表
const handlePrint = () => {
  if (!cardTableConfig.dataList.length) {
    SLMessage.warning('暂无数据可打印')
    return
  }

  const params = cardSearch.value?.queryParams
  const reportTypeLabel = reportType.find(item => item.value === params?.type)?.label || '报表'

  printJSON({
    title: `水厂吨水电耗${reportTypeLabel}`,
    data: cardTableConfig.dataList,
    columns: cardTableConfig.columns
  })
}

// 组件挂载时初始化
onMounted(async () => {
  await getTreeData()
  if (state.stationId) {
    await refreshData()
  }
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  height: 100%;
}

.list-layout,
.chart-layout {
  display: flex;
  height: 100%;
  gap: 16px;
}

.table-container,
.chart-container {
  flex: 1;
  min-height: 0;
}

.card-table {
  height: 100%;
}

.line-chart {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.date-selector {
  width: 200px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.selector-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
}

.date-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-btn {
  width: 100%;
  justify-content: flex-start;
}

// 深色模式适配
:deep(.el-card) {
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color);
}

:deep(.el-table) {
  background-color: var(--el-bg-color);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page);
}

// 响应式布局
@media (max-width: 1200px) {
  .list-layout,
  .chart-layout {
    flex-direction: column;
  }

  .date-selector {
    width: 100%;

    .date-buttons {
      flex-direction: row;
      flex-wrap: wrap;
    }

    .date-btn {
      width: auto;
      flex: 1;
      min-width: 80px;
    }
  }
}
</style>
