import * as echarts from 'echarts'
import { transNumberUnit } from '@/utils/GlobalHelper'

export function lineOption(datex?: any, data1?: any, data2?: any, left?: number, right?: number) {
  const option = {
    color: ['#318DFF', '#FFB800', '#FC2B2B'],
    grid: {
      left: left || 80,
      right: right || 80,
      top: 60,
      bottom: 40
    },
    legend: {
      // top: 10,
      width: '500',
      type: 'scroll',
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: datex
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '供水量(m³)',
      splitNumber: 5,
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    },
    {
      position: 'right',
      type: 'value',
      name: '',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        name: '出口压力(Mpa)',
        smooth: true,
        data: data1,
        type: 'line'
      },
      {
        name: '瞬时流量(m³/h)',
        smooth: true,
        data: data2,
        type: 'line',
        yAxisIndex: 1
      }
    ]
  }
  return option
}

export const pieOption = (title: string, color: string, data: any) => {
  const option = {
    color: [
      new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color
        },
        {
          offset: 1,
          color: 'rgba(21, 45, 68,0.5)'
        }
      ]),
      'rgba(21, 45, 68,0.5)'
    ],
    title: {
      text: title,
      bottom: 0,
      left: 'center',
      textStyle: {
        color: 'rgb(137, 168, 195)',
        fontSize: 13
      }
    },
    series: [
      {
        startAngle: -60,
        name: 'Access From',
        type: 'pie',
        radius: ['62%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'center',
          formatter() {
            return (data || 0) + '\n' + 'm³'
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '12',
            fontWeight: 'bold'
          }
        },
        data: [
          { value: 10, name: '' },
          { value: 4, name: '' }
        ]
      }
    ]
  }
  return option
}

export const barOption = (title?: string, color?: string, dataX?: any[], data?: any[], left?: number, right?: number) => {
  const option = {
    color: ['#318DFF', '#43B530', '#FC2B2B'],
    xAxis: {
      type: 'category',
      data: dataX
    },
    grid: {
      left: left || 50,
      right: right || 50,
      top: 50,
      bottom: 50
    },
    legend: {
      // right: 150,
      // top: 10,
      width: '500',
      type: 'scroll',
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '吨水能耗(kw.h/m³)',
      axisLine: {
        show: true,
        lineStyle: {
          // color: '#ffffff' // '#333'
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          // fontSize: 14 //更改坐标轴文字大小
        }
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [
      {
        data,
        type: 'bar'
      }
    ]
  }
  return option
}

// 水质监测线图配置
export function waterQualityLineOption(width?: number, datex?: any, left?: number, right?: number) {
  const option = {
    grid: {
      left: left || 80,
      right: right || 80,
      top: 80,
      bottom: 50
    },
    legend: {
      type: 'scroll',
      width: width || 200,
      textStyle: {
        fontSize: 12
      }
    },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: datex
    },
    yAxis: [{
      position: 'left',
      type: 'value',
      name: '水质指标',
      axisLine: {
        show: true,
        lineStyle: {
          types: 'solid'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {}
      },
      splitLine: {
        lineStyle: {
          type: [5, 10],
          dashOffset: 5
        }
      }
    }],
    series: [{}]
  }
  return option
}

// 水质监测饼图配置
export const waterQualityPieOption = (title?: string, data?: any) => {
  const option = {
    color: ['red', 'blue', 'orange'],
    legend: {
      type: 'scroll',
      orient: 'vertical',
      top: 'center',
      right: 'right',
      width: 200,
      formatter: '{name}{value}',
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    title: {
      text: title,
      bottom: 0,
      left: 'center',
      textStyle: {
        color: 'rgb(137, 168, 195)',
        fontSize: 13
      }
    },
    graphic: {
      elements: [
        {
          type: 'image',
          style: {
            image: 'https://yp.a-hh.cn/zhjk/img.jpg',
            width: 25,
            height: 30
          },
          left: 'center',
          top: 'center'
        }]
    },
    series: [
      {
        startAngle: -60,
        name: 'Access From',
        type: 'pie',
        radius: ['62%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
          color: '#ffffff',
          formatter() {
            return (data || 0) + '\n' + 'mg/L'
          }
        },
        data: [
          { value: 0, name: '报警' },
          { value: 4, name: '正常' },
          { value: 0, name: '异常' }
        ]
      }
    ]
  }
  return option
}

// 水质监测环形图配置
export const waterQualityRing = (
  data: {
    name: string
    nameAlias?: string
    value: string
    valueAlias?: string
    scale: string
  }[] = [],
  unit?: string,
  prefix?: string,
  percision = 2
) => {
  const title = '总数'
  const formatNumber = function (num) {
    const reg = /(?=(\B)(\d{3})+$)/g
    return num.toString().replace(reg, ',')
  }
  const total = data.reduce((a, b: any) => {
    return a + (parseFloat(b.value) || 0) * 1
  }, 0)
  const transedTotal = transNumberUnit(total)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return (
          (prefix || '')
          + params.name
          + ': '
          + Number(params.value).toFixed(percision)
          + ' '
          + unit
        )
      }
    },
    legend: {
      type: 'scroll',
      icon: 'circle',
      orient: 'vertical',
      left: 'right',
      top: 'center',
      align: 'left',
      itemGap: 10,
      itemWidth: 10,
      itemHeight: 10,
      symbolKeepAspect: true,
      textStyle: {
        color: '#fff',
        rich: {
          name: {
            align: 'left',
            width: 80,
            fontSize: 12
          },
          value: {
            align: 'left',
            width: 70,
            fontSize: 12
          },
          count: {
            align: 'left',
            width: 70,
            fontSize: 12
          },
          upRate: {
            align: 'left',
            fontSize: 12
          },
          downRate: {
            align: 'left',
            fontSize: 12
          }
        }
      },
      data: data.map(item => item.name),
      formatter(name) {
        if (data && data.length) {
          for (let i = 0; i < data.length; i++) {
            if (name === data[i].name) {
              return (
                '{name| '
                + (data[i].nameAlias || name)
                + '}'
                + '{value| '
                + (data[i].valueAlias || data[i].value)
                + ' '
                + (unit || '')
                + '}'
                + '{value| '
                + (data[i].scale || '')
                + '}'
              )
            }
          }
        }
      }
    },
    title: [
      {
        text:
          '{name|'
          + title
          + ((unit && '(' + transedTotal.unit + unit + ')')
            || '(' + transedTotal.unit + ')')
          + '}\n{val|'
          + formatNumber(transedTotal.value.toFixed(percision))
          + '}',
        top: 'center',
        left: '19%',
        textAlign: 'center',
        textStyle: {
          rich: {
            name: {
              fontSize: 10,
              fontWeight: 'normal',
              padding: [8, 0],
              align: 'center',
              color: '#fff'
            },
            val: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#fff'
            }
          }
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['45%', '60%'],
        center: ['20%', '50%'],
        data,
        hoverAnimation: false,
        label: {
          show: false,
          formatter: params => {
            return (
              '{icon|●}{name|'
              + params.name
              + '}{value|'
              + formatNumber(Number(params.value || '0').toFixed(percision))
              + '}'
            )
          },
          padding: [0, -100, 25, -100],
          rich: {
            icon: {
              fontSize: 16
            },
            name: {
              fontSize: 14,
              padding: [0, 10, 0, 4]
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold'
            }
          }
        }
      }
    ]
  }
  return option
}
