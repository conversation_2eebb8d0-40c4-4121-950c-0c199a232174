<template>
  <RightDrawerMap
    ref="refMap" :title="'水质监测总览'" :windows="state.windows" :hide-detail-close="true"
    :hide-layer-list="true" :right-drawer-width="540" @map-loaded="onMapLoaded"
  >
    <div class="content">
      <Form ref="refForm" :config="FormConfig">
      </Form>
      <!--      </div>-->
      <FieldSet type="underline" title="水质数据监测"></FieldSet>
      <div class="right-box">
        <FormTable ref="refCard" class="table-box" :config="cardTableConfig"></FormTable>
      </div>
      <FieldSet type="underline" :title="(state.curRow?.name || '') + '近三天运行曲线'"></FieldSet>
      <div ref="echartsDiv" class="right-box bottom">
        <Tabs v-model="state.activeName" :config="TabsConfig"></Tabs>
        <VChart ref="refChart" :theme="useAppStore().isDark ? 'dark' : 'light'" :option="state.lineOption" />
      </div>
    </div>
    <template #detail-header>
    </template>
    <template #detail-default>
    </template>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import Point from '@arcgis/core/geometry/Point.js'
import Graphic from '@arcgis/core/Graphic.js'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol.js'
import elementResizeDetectorMaker from 'element-resize-detector'
import TextSymbol from '@arcgis/core/symbols/TextSymbol.js'
import useStation from '@/hooks/station/useStation'
import { GetStationRealTimeDetail } from '@/api/shuiwureports/zhandian'
import { IECharts } from '@/plugins/echart'
import { useAppStore, useBusinessStore } from '@/store'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import { bindViewClick, gotoAndHighLight } from '@/utils/MapHelper'
import { stationView } from '@/api/pipeNetworkMonitoring/flowMonitoring'
import { getThreeDaysData } from '@/api/headwatersManage/headwaterMonitoring'
import { lineOption, pieOption } from '../echartsData/echart'
import { ring } from '@/views/arcMap/components/components/chart'
import { GetWaterQualityStaionList } from '@/api/mapservice/onemap'
import { getStationImageUrl } from '@/utils/URLHelper'

const erd = elementResizeDetectorMaker()
const refCard = ref<IFormTableIns>()
const refAmap = ref<ISLAmapIns>()
const { getAllStationOption } = useStation()

const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const refChart = ref<IECharts>()
const loading = ref<boolean>(true)

const echartsDiv = ref<any>()
const tableData = reactive<any[]>([])

const statusOptions = [
  { name: 'offline', label: '离线' },
  { name: 'alarm', label: '报警' },
  { name: 'online', label: '正常' }
]
const state = reactive<{
  lineOption: any,
  pieOption: any,
  curRow?: any,
  activeName: any,
  stationStatus: any,
  data: any,
  stationLocation: any[]
  tableData: any[]
  windows: IArcMarkerProps[]
}>({
  lineOption: null,
  pieOption: null,
  stationStatus: [],
  curRow: {},
  activeName: null,
  data: null,
  stationLocation: [],
  tableData: [],
  windows: []
})

const staticState: {
  view?: __esri.MapView
} = {}

const handleMarkClick = async (stationId?: string) => {
  const tableRow = cardTableConfig.dataList.find(
    item => item.stationId === stationId
  )
  const station = state.tableData.find(item => item.stationId === stationId)
  TabsConfig.tabs = station?.dataList?.map((d, i) => {
    return {
      label: d.propertyName,
      value: d.property + i,
      data: d
    }
  })
  state.activeName = TabsConfig.tabs ? TabsConfig.tabs[0].value : ''
  state.curRow = tableRow
  resetPanel(tableRow, TabsConfig.tabs ? TabsConfig.tabs[0].data : {})
  // await refuseChart(TabsConfig.tabs[0].data)
  let graphic: __esri.Graphic | undefined
  if (!stationId) {
    graphic = staticState.view?.graphics?.getItemAt(0)
  } else {
    graphic = staticState.view?.graphics.find(
      item => item.attributes?.row?.id === stationId
    )
    if (graphic) {
      await gotoAndHighLight(staticState.view, graphic, {
        zoom: 15,
        avoidHighlight: true
      })
    }
  }
  if (!graphic) return

  const attributes = graphic.attributes?.row || {}
  const res = await GetStationRealTimeDetail(attributes.id)
  const values = res.data?.map(item => {
    item.label = item.propertyName
    item.value

    return item
  }) || []
  const point = graphic?.geometry as __esri.Point
  state.windows.length = 0
  state.windows.push({
    visible: false,
    x: point.x,
    y: point.y,
    offsetY: -30,
    title: attributes.name,
    attributes: {
      values,
      id: attributes.id
    }
  })
  await nextTick()
  refMap.value?.openPop(attributes.id)
}

const TabsConfig = reactive<ITabs>({
  type: 'tabs',
  tabType: 'simple',
  tabs: [],
  handleTabClick: (tab, event) => {
    const tabData = TabsConfig.tabs.find(tb => tb.value === tab.props.name) as any || {}
    resetPanel(state.curRow, tabData.data)
  }
})

const FormConfig = reactive<IFormConfig>({
  group: [
    {
      id: 'chart',
      fieldset: {
        desc: '水质监测设备',
        type: 'underline',
        style: {
          marginTop: 0
        }
      },
      fields: [
        {
          type: 'vchart',
          option: ring(),
          style: {
            height: '150px'
          }
        }
      ]
    }
  ],
  labelPosition: 'top',
  gutter: 12,
  defaultValue: {
    type: 'all'
  }
})

//
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  highlightCurrentRow: true,
  currentRowKey: 'stationId',
  handleRowClick: async (row: any) => {
    // const mapRow = state.stationLocation?.filter(item => item.id === row.stationId)[0]
    const g = staticState.view?.graphics.find(
      item => item.attributes?.row?.id === row.stationId
    )
    g
      && (await gotoAndHighLight(staticState.view, g, {
        zoom: 15,
        avoidHighlight: true
      }))

    const station = state.tableData.find(item => item.stationId === row.stationId)
    TabsConfig.tabs = station?.dataList?.map((d, i) => {
      return {
        label: d.propertyName,
        value: d.property + i,
        data: d
      }
    })
    state.activeName = TabsConfig.tabs[0].value
    resetPanel(TabsConfig.tabs[0].data)
    cardTableConfig.currentRow = row
    state.curRow = row
    handleMarkClick(row.stationId)
  },
  pagination: { hide: true }
})

// 刷新列表 模拟数据
const refreshData = async () => {
  stationView({
    stationType: '水质监测站',
    projectId: useBusinessStore().selectedProject?.value
  }).then(res => {
    state.tableData = res.data
    if (state.tableData) {
      const cs = state.tableData[0]?.dataList?.map(row => {
        return { prop: row.property, label: row.propertyName, unit: row.unit }
      })
      cardTableConfig.loading = false
      const newData = state.tableData?.map(row => {
        const property = row.dataList?.find(data => data.property === 'pressure')
        const newData = {
          name: row.name,
          stationId: row.stationId,
          time: property ? property.time : '-',
          unit: property ? property.unit : '-'
        }
        row?.dataList?.map(d => {
          newData[d.property + ''] = d.value
        })
        return newData
      })
      addMarks(newData)
      cardTableConfig.columns = [{
        prop: 'name',
        label: '名称',
        minWidth: 200
      }].concat(cs)
      cardTableConfig.dataList = newData
      // cardTableConfig.currentRow = newData[0]
      state.activeName = (state.tableData[0]?.dataList[0])
      cardTableConfig.currentRow = newData[0]
      const station = state.tableData.find(item => item.stationId === newData[0]?.stationId)
      TabsConfig.tabs = station?.dataList?.map((d, i) => {
        return {
          label: d.propertyName,
          value: d.property + i,
          data: d
        }
      })
      state.activeName = TabsConfig.tabs ? TabsConfig.tabs[0].value : ''
      resetPanel(TabsConfig.tabs ? TabsConfig.tabs[0].value : '')
      console.log('resetPanel', state.tableData[0]?.stationId)
      handleMarkClick(state.tableData[0]?.stationId)
    } else {
      cardTableConfig.loading = false
      loading.value = false
    }
  })
  await alarmNum()
}

// 告警饼图统计
const alarmNum = async () => {
  // const res = await GetStationList({
  //   page: 1,
  //   size: 999,
  //   projectId: useBusinessStore().selectedProject?.value,
  //   type: '水质监测站'
  // })
  const res = await GetWaterQualityStaionList({ projectId: useBusinessStore().selectedProject?.value, status: '' })
  const field = FormConfig.group[0].fields[0] as IFormVChart
  const total: number = res.data?.data?.length || 0
  const statusDatas: any[] = []
  const data = res.data?.data
  data?.map(item => {
    let statusData = statusDatas.find(o => o.status === item.status)
    const { label } = statusOptions.find(o => o.name === item.status) || {}
    if (!statusData) {
      statusData = {
        name: item.status,
        status: item.status,
        nameAlias: label,
        value: 1,
        // valueAlias: '1',
        scale: '0%'
      }
      statusDatas.push(statusData)
    } else {
      statusData.value++
    }
  })
  statusDatas.map(item => {
    item.scale = total === 0 ? '0%' : (Number(item.value) / total) * 100 + '%'
    return item
  })
  field && (field.option = ring(statusDatas, '个'))
}
// 告警饼图统计
// const alarmNum1 = async () => {
//   const res = await GetPressureStationList({ status: '' })
//   const field = FormConfig.group[0].fields[0] as IFormVChart
//   const total: number = res.data?.data?.length || 0
//   const statusDatas: any[] = []
//   const data = res.data?.data
//   data?.map(item => {
//     let statusData = statusDatas.find(o => o.status === item.status)
//     const { label } = statusOptions.find(o => o.name === item.status) || {}
//     if (!statusData) {
//       statusData = {
//         name: label,
//         status: item.status,
//         nameAlias: label,
//         value: 1,
//         // valueAlias: '1',
//         scale: '0%'
//       }
//       statusDatas.push(statusData)
//     } else {
//       statusData.value++
//     }
//   })
//   statusDatas.map(item => {
//     item.scale = total === 0 ? '0%' : (Number(item.value) / total) * 100 + '%'
//     return item
//   })
//   field && (field.option = ring(statusDatas, '个'))
// }

//  获取监控数据已经加载显示图表
const resetPanel = async (row, data?: any) => {
  cardTableConfig.currentRow = row
  if (data || row) {
    await refuseChart(data || row)
    nextTick(async () => {
      state.pieOption = pieOption()
    })
  }
  loading.value = false
}

// 加载图表
const refuseChart = async (data: any) => {
  const threeDaysData = await getThreeDaysData({ deviceId: data.deviceId, attr: data.property })
  const tData = threeDaysData.data?.data
  const options = lineOption(200, tData.todayDataList.map(item => item.ts), 40, 40)
  const dataMap = [
    { name: '前天', key: 'beforeYesterdayDataList' },
    { name: '昨天', key: 'yesterdayDataList' },
    { name: '今天', key: 'todayDataList' }
  ]
  options.yAxis[0].name = data.propertyName.concat(data.unit ? '(' + data.unit + ')' : '')
  const series = dataMap.map(item => {
    const data = tData[item.key].map(item => item.value)
    return {
      name: item.name,
      smooth: true,
      data,
      type: 'line',
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }
  })
  options.series = series
  refChart.value?.clear()
  await nextTick(() => {
    if (echartsDiv.value) {
      erd.listenTo(echartsDiv.value, () => {
        state.lineOption = options
        state.pieOption = pieOption()
        refChart.value?.resize()
      })
    }
  })
}

// 添加地图图标
const addMarks = async (realTimeData: any) => {
  const res = await getAllStationOption(
    '水质监测站'
  )

  state.stationStatus = res
  console.log('水质监测站', res)
  staticState.view?.graphics?.removeAll()
  res.map(data => {
    const item = data.data
    const location = item?.location?.split(',')
    const point = new Point({
      longitude: location?.[0],
      latitude: location?.[1],
      spatialReference: staticState.view?.spatialReference
    })
    const status = state.stationStatus?.find(station => station.id === data.id)
    console.log('status', status.status)
    // TODO 切换异常图片
    const url = status.status === 'online' ? getStationImageUrl('水质监测站.png') : getStationImageUrl('水质监测站.png')
    const markG = new Graphic({
      geometry: point,
      symbol: new PictureMarkerSymbol({
        width: 25,
        height: 30,
        yoffset: 15,
        url
      }),
      attributes: {
        row: item
      }
    })
    const pressureData = realTimeData?.find(pressure => pressure.stationId === item.id)
    const markText = new Graphic({
      geometry: point,
      symbol: new TextSymbol({
        yoffset: -15,
        color: '#00ff33',
        text: pressureData ? pressureData.name : '-'
      })
    })
    staticState.view?.graphics?.addMany([markG, markText])
  })
  state.stationLocation = res
  state.curRow = res[0]?.data
  cardTableConfig.currentRow = state.curRow
  handleMarkClick(state.curRow?.id)
}

// 显示地图弹出层
const addInfoWindow = async (row: any) => {
  const res = await GetStationRealTimeDetail(row.stationId || row.id)
  const values = res.data?.map(item => {
    // item.value = (item.value || '') + ' ' + (item.unit || '')
    item.label = item.propertyName
    return item
  }) || []
  const location = row.location?.split(',')
  location?.length === 2
    && refAmap.value?.setListInfoWindow({
      point: location,
      values,
      title: row.name
    })
}

// watch(() => useAppStore().isDark, () => {
//   handleMarkClick(cardTableConfig.currentRow, state.curRow)
// })

// onMounted(async () => {
//   refPanelBottom.value?.Open()
//   refPanelRight.value?.Open()
//   refreshData()
// })

const onMapLoaded = async (view: __esri.MapView) => {
  staticState.view = view
  refMap.value?.toggleCustomDetail(false)
  await refreshData()
  bindViewClick(staticState.view, res => {
    const result = res.results?.[0]
    if (!result) return
    if (result.type === 'graphic') {
      const row = result.graphic?.attributes?.row
      handleMarkClick(row?.id)
    }
  })
}
</script>
<style lang="scss" scoped>
.right-box {
  width: 100%;
  height: 250px;

  .table-box {
    height: 90%
  }
}

.top {
  height: calc(20vh - 20px);
}

.bottom {
  height: calc(33vh - 20px);
}

.today-lightning-total,
.today-lightning-perton {
  display: flex;

  .count {
    font-size: 18px;
    color: #ffad51;
    font-weight: 500;
  }

  .unit {
    font-size: 12px;
  }

  .yesterday {
    margin-left: auto;
    font-size: 12px;
    padding-top: 10px;
  }
}

.today-lightning-perton {
  .count {
    color: #ff51ab;
  }
}

.header-slot {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title {
    word-break: keep-all;
  }
}
</style>
<style lang="scss">
.panel {
  &.map-right {
    right: 0;
  }

  &.map-right {
    top: 0;
    width: 400px;
    height: 100%;

    .content {
      width: 100%;
      height: calc(100% - 15px);
    }
  }
}
</style>
