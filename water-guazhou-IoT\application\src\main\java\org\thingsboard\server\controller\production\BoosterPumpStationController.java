package org.thingsboard.server.controller.production;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.dao.pumpStation.PumpStationMonitorService;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Utils.DateUtils;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.model.VO.DynamicTableVO;
import org.thingsboard.server.dao.model.VO.StationStatusVO;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.production.ProductionService;
import org.thingsboard.server.dao.stationData.StationDataService;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 智慧生产-加压泵站/二供泵房管理
 */
@Slf4j
@RestController
@RequestMapping("api/boosterPumpStation")
public class BoosterPumpStationController extends BaseController {

    @Autowired
    private ProductionService productionService;

    @Autowired
    private StationFeignClient stationFeignClient;

    @Autowired
    private StationDataService stationDataService;

    @Autowired
    private PumpStationMonitorService pumpStationMonitorService;

    @GetMapping("getWaterSupplyInfo")
    public IstarResponse getWaterSupplyInfo(@RequestParam(required = false, defaultValue = "") String projectId,
                                            @RequestParam(required = false, defaultValue = "") String name) throws ThingsboardException {
        return IstarResponse.ok(productionService.getWaterSupplyInfo(name, DataConstants.StationType.PUMP_STATION.getValue(), projectId, getTenantId()));
    }

    /**
     * 盐亭定制  梓莲达供水
     *
     * @return
     * @throws ThingsboardException
     */
    @GetMapping("getWaterSupplyInfoTotal")
    public IstarResponse getWaterSupplyInfo() throws ThingsboardException {
        String time = new SimpleDateFormat("yyyyMMddHH").format(new Date());
        return IstarResponse.ok(productionService.getWaterSupplyTotal(time, DataConstants.StationType.PUMP_STATION.getValue(), getTenantId()));
    }

    @GetMapping("getWaterSupplyDetail")
    public IstarResponse getWaterSupplyDetail(@RequestParam String stationId) throws ThingsboardException {
        StationEntity station = stationFeignClient.get(stationId);
        if (station == null) {
            return IstarResponse.error("要查询的泵站不存在, 泵站ID: " + stationId);
        }
        // 供水曲线数据
        JSONObject waterSupplyDetail = productionService.getWaterSupplyDetail(stationId, getTenantId());

        // 今日出水压力、今日出口瞬时流量
        List<String> attrList = new ArrayList<>();
        attrList.add(DataConstants.DeviceAttrType.PRESSURE.getValue());
        attrList.add(DataConstants.DeviceAttrType.INSTANTANEOUS_FLOW.getValue());
        // 今日时间
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date todayStart = instance.getTime();

        Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                stationId, DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue(), attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());

        for (Map.Entry<String, List<JSONObject>> entry : stationDataMap.entrySet()) {
            waterSupplyDetail.put(entry.getKey(), entry.getValue());
        }

        return IstarResponse.ok(waterSupplyDetail);
    }


    /**
     * 获取指定站点的取水供水信息
     * 包含：今日取水量、今日供水量、昨日供水量、本月供水量
     */
    @GetMapping("gis/getWaterInfo")
    public IstarResponse getWaterInfo(@RequestParam String stationId) throws ThingsboardException {
        try {
            return IstarResponse.ok(productionService.getWaterInfo(stationId, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询二供泵房供水量总览
     */
    @GetMapping("getWaterSupplyInfoView")
    public IstarResponse getWaterSupplyInfoView(@RequestParam(required = false, defaultValue = "") String projectId) throws ThingsboardException {
        return IstarResponse.ok(productionService.getWaterSupplyInfoView(DataConstants.StationType.PUMP_STATION.getValue(), projectId, getTenantId()));
    }

    /**
     * 查询二供泵房监测数据项实时数据
     */
    @GetMapping("getWaterSupplyInfoDetail")
    public IstarResponse getWaterSupplyInfoDetail(@RequestParam(required = false, defaultValue = "") String projectId) throws ThingsboardException {
        return IstarResponse.ok(stationDataService.getStationDataDetailGroupListView(DataConstants.StationType.PUMP_STATION.getValue(), projectId, getTenantId()));
    }

    /**
     * 查询指定站点的水量报表
     */
    @GetMapping("getWaterSupplyReport")
    public IstarResponse getWaterSupplyReport(@RequestParam Long start, @RequestParam Long end,
                                              @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyReport(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定站点的水量报表
     */
    @GetMapping("getWaterSupplyReportByProject")
    public IstarResponse getWaterSupplyReportByProject(@RequestParam Long start, @RequestParam Long end,
                                                       @RequestParam(required = false) String projectId, @RequestParam String queryType) {
        try {
            // 查询站点
            PageData<StationEntity> stationPageData = stationFeignClient.list(1, 99999, DataConstants.StationType.PUMP_STATION.getValue(), projectId);
            if (stationPageData.getData() == null || stationPageData.getData().isEmpty()) {
                return IstarResponse.error("该区域下没有配置" + DataConstants.StationType.PUMP_STATION.getValue());
            }

            Map<String, JSONObject> resultMap = new HashMap<>();
            List<StationEntity> stationList = stationPageData.getData();
            for (StationEntity station : stationList) {
                DynamicTableVO dynamicTableVO = productionService.getWaterSupplyReport(station.getId(), start, end, queryType, getTenantId());

                List<JSONObject> tableDataList = dynamicTableVO.getTableDataList();
                for (JSONObject data : tableDataList) {
                    String ts = data.getString("ts");
                    if (!ts.contains("日") || data.getDouble("total") == null) {
                        continue;
                    }
                    String newTs = ts.replaceAll("日", "");
                    JSONObject newData = new JSONObject();

                    Double total = data.getDouble("total");
                    if (resultMap.containsKey(newTs)) {
                        newData = resultMap.get(newTs);
                    } else {
                        newData.put("total", 0);
                    }
                    newData.put("ts", ts);
                    newData.put("total", total + newData.getDoubleValue("total"));

                    resultMap.put(newTs, newData);
                }
            }
            List<String> keyList = new ArrayList<>(resultMap.keySet());
            // 排序
            keyList.sort(String::compareTo);
            List<JSONObject> resultList = new ArrayList<>();
            for (String key : keyList) {
                JSONObject data = resultMap.get(key);
                resultList.add(data);
            }

            return IstarResponse.ok(resultList);
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定站点的能耗报表
     */
    @GetMapping("getEnergyMethodReport")
    public IstarResponse getEnergyMethodReport(@RequestParam Long start, @RequestParam Long end,
                                               @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getEnergyMethodReport(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询指定站点的单耗
     */
    @GetMapping("getWaterSupplyConsumptionReport")
    public IstarResponse getWaterSupplyConsumptionReport(@RequestParam Long start, @RequestParam Long end,
                                                         @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyConsumptionReport(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询二供泵房列表的电耗数据
     * 包含：本期供水量、本期耗电量、本期上期吨水电耗、吨水电耗差值、变化率
     */
    @GetMapping("getWaterSupplyAndEnergyData")
    public IstarResponse getWaterSupplyAndEnergyData(@RequestParam Long start, @RequestParam Long end, @RequestParam String queryType,
                                                     @RequestParam(required = false) String name) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyAndEnergyData(DataConstants.StationType.PUMP_STATION.getValue(), start, end, queryType, name, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询单个二供泵房的耗电数据详情
     */
    @GetMapping("getWaterSupplyAndEnergyDataDetail")
    public IstarResponse getWaterSupplyAndEnergyDataDetail(@RequestParam Long start, @RequestParam Long end,
                                                           @RequestParam String stationId, @RequestParam String queryType) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyAndEnergyDataDetail(stationId, queryType, start, end, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 获取平衡分析数据
     */
    @GetMapping("getBalanceReport")
    public IstarResponse getBalanceReport(@RequestParam String queryType, @RequestParam String stationId,
                                          @RequestParam Long start, @RequestParam Long end) {
        try {
            return IstarResponse.ok(productionService.getBalanceReport(start, end, queryType, stationId, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询供水概览
     */
    @GetMapping("getWaterSupplyOverview")
    public IstarResponse getWaterSupplyOverview(@RequestParam Long start, @RequestParam Long end, @RequestParam String queryType,
                                                @RequestParam(required = false) String name) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyOverview(DataConstants.StationType.PUMP_STATION.getValue(), start, end, queryType, name, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    /**
     * 查询供水概览曲线趋势
     */
    @GetMapping("getWaterSupplyOverviewTrend")
    public IstarResponse getWaterSupplyOverviewTrend(@RequestParam Long start, @RequestParam Long end,
                                                     @RequestParam String queryType, @RequestParam String stationId) {
        try {
            return IstarResponse.ok(productionService.getWaterSupplyOverviewTrend(stationId, start, end, queryType, getTenantId()));
        } catch (ThingsboardException e) {
            return IstarResponse.error(e.getMessage());
        }
    }

    @GetMapping("getList")
    public IstarResponse getList(@RequestParam(required = false, defaultValue = "") String projectId,
                                 @RequestParam(required = false, defaultValue = "") String name,
                                 @RequestParam String status) throws ThingsboardException {
        String type = DataConstants.StationType.PUMP_STATION.getValue();
        return IstarResponse.ok(productionService.getList(type, projectId, name, status, getTenantId()));
    }

    @GetMapping("stationStatusCount")
    public IstarResponse alarmCount(@RequestParam(required = false, defaultValue = "") String projectId,
                                    @RequestParam(required = false, defaultValue = "") String name,
                                    @RequestParam String status) throws ThingsboardException {
        String type = DataConstants.StationType.PUMP_STATION.getValue();
        List<StationStatusVO> list = productionService.getList(type, projectId, name, status, getTenantId());
        JSONObject result = new JSONObject();
        int online = 0;
        int offline = 0;
        int alarm = 0;
        if (list != null && !list.isEmpty()) {

            for (StationStatusVO stationStatusVO : list) {
                String stationStatus = stationStatusVO.getStatus();
                if ("online".equals(stationStatus)) {
                    online = online + 1;
                }
                if ("offline".equals(stationStatus)) {
                    offline = offline + 1;
                }
                if ("alarm".equals(stationStatus)) {
                    alarm = alarm + 1;
                }
            }
        }
        result.put("online", online);
        result.put("offline", offline);
        result.put("alarm", alarm);

        return IstarResponse.ok(result);
    }

    @GetMapping("getStationDataByAttr")
    public IstarResponse getStationDataByAttr(@RequestParam String stationIds, @RequestParam String attr) throws ThingsboardException {
        JSONObject result = new JSONObject();
        String[] stationArray = stationIds.split(",");
        for (String stationId : stationArray) {
            // 今日出水压力、今日出口瞬时流量
            List<String> attrList = new ArrayList<>();
            attrList.add(attr);
            // 今日时间
            Calendar instance = Calendar.getInstance();
            instance.set(Calendar.HOUR_OF_DAY, 0);
            instance.set(Calendar.MINUTE, 0);
            instance.set(Calendar.SECOND, 0);
            Date todayStart = instance.getTime();

            Map<String, List<JSONObject>> stationDataMap = productionService.getStationData(
                    stationId, DataConstants.DeviceAttrGroupType.WATER_OUTLET.getValue(), attrList, DateUtils.HOUR, todayStart, new Date(), getTenantId());
            List<JSONObject> dataList = stationDataMap.get(attr);
            result.put(stationId, dataList);
        }

        return IstarResponse.ok(result);
    }

    // ==================== 泵站监控专用接口 ====================

    @ApiOperation(value = "获取泵站监控数据")
    @GetMapping("/monitorData")
    public IstarResponse getMonitorData(
            @RequestParam(required = false) String stationIds,
            @RequestParam(required = false, defaultValue = "day") String timeGranularity,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false, defaultValue = "electric") String pumpType
    ) throws ThingsboardException {
        try {
            List<String> stationIdList = null;
            if (stationIds != null && !stationIds.isEmpty()) {
                stationIdList = Arrays.asList(stationIds.split(","));
            }

            Map<String, Object> result = pumpStationMonitorService.getMonitorData(
                    stationIdList, timeGranularity, startTime, endTime, pumpType, getTenantId());
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取泵站监控数据失败", e);
            return IstarResponse.error("获取泵站监控数据失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取泵站详情")
    @GetMapping("/detail")
    public IstarResponse getStationDetail(
            @RequestParam(required = false) String stationIds
    ) throws ThingsboardException {
        try {
            List<String> stationIdList = null;
            if (stationIds != null && !stationIds.isEmpty()) {
                stationIdList = Arrays.asList(stationIds.split(","));
            }

            List<Map<String, Object>> result = pumpStationMonitorService.getStationDetail(stationIdList, getTenantId());
            return IstarResponse.ok(result);
        } catch (Exception e) {
            log.error("获取泵站详情失败", e);
            return IstarResponse.error("获取泵站详情失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "应用泵站方案")
    @PostMapping("/applyScheme")
    public IstarResponse applyScheme(@RequestBody Map<String, Object> params) throws ThingsboardException {
        try {
            String schemeId = (String) params.get("schemeId");
            List<String> stationIds = (List<String>) params.get("stationIds");

            pumpStationMonitorService.applyScheme(schemeId, stationIds);
            return IstarResponse.ok("方案应用成功");
        } catch (Exception e) {
            log.error("应用泵站方案失败", e);
            return IstarResponse.error("应用泵站方案失败: " + e.getMessage());
        }
    }

}
