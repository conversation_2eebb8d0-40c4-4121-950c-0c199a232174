<!-- 泵组运行状态分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    
    <div class="content-container">
      <!-- 左侧表格 -->
      <div class="left-panel">
        <SLCard title="泵组运行状态分析">
          <CardTable
            ref="refTable"
            class="card-table"
            :config="cardTableConfig"
          />
        </SLCard>
      </div>
      
      <!-- 右侧图表 -->
      <div class="right-panel">
        <SLCard title="">
          <div class="chart-container">
            <VChart
              ref="refChart"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.chartOption"
            ></VChart>
          </div>
        </SLCard>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import elementResizeDetectorMaker from 'element-resize-detector'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { ISearchIns, ICardTableIns } from '@/components/type'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyAndEnergyData } from '@/api/secondSupplyManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { horizontalBarOption } from './echartsData/echart'
import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'

const { getStationTree } = useStation()
const erd = elementResizeDetectorMaker()

const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  title: string,
  chartOption: any,
  dataList: any
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  title: '',
  chartOption: null,
  dataList: {}
})

const refTable = ref<ICardTableIns>()
const cardSearch = ref<ISearchIns>()
const refChart = ref<IECharts>()

// 泵站站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format('YYYY'), dayjs().format('YYYY')],
    month: [dayjs().format('YYYY-MM'), dayjs().format('YYYY-MM')],
    day: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        TreeData.currentProject = val
        state.treeDataType = val.data?.type as string
        if (state.treeDataType === 'Station') {
          state.stationId = val.id as string
          // 站点改变时自动刷新数据
          nextTick(() => {
            refreshData()
          })
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        { perm: true, text: '查询', svgIcon: shallowRef(Search), click: () => refreshData() },
        {
          perm: true,
          text: '导出',
          type: 'success',
          svgIcon: shallowRef(Download),
          click: () => _exportOperationStatus()
        },
        {
          perm: true,
          text: '打印',
          type: 'warning',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 表格配置
const cardTableConfig = reactive<ICardTable>({
  loading: false,
  columns: [
    { prop: 'pumpName', label: '站点名称', minWidth: 120, align: 'left' as const },
    { prop: 'yesterdayRunTime', label: '昨日运行（小时）', minWidth: 130, align: 'center' as const },
    { prop: 'todayRunTime', label: '今日运行（小时）', minWidth: 130, align: 'center' as const },
    { prop: 'stopTime', label: '停机时长（小时）', minWidth: 130, align: 'center' as const }
  ],
  dataList: [],
  pagination: {
    hide: true
  }
})

// 刷新数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const { type = 'day' } = queryParams
  const date = queryParams[type]

  if (!date || !date[0] || !date[1]) {
    cardTableConfig.loading = false
    return
  }

  // 检查是否选择了站点
  if (!state.stationId) {
    cardTableConfig.loading = false
    return
  }

  const stationName = TreeData.currentProject?.label || '未知泵站'
  state.title = `${stationName}运行状态分析`

  const [start, end] = date
  const params = {
    stationId: state.stationId,
    start: dayjs(start).startOf(type === 'day' ? 'day' : type === 'month' ? 'month' : 'year').valueOf(),
    end: dayjs(end).endOf(type === 'day' ? 'day' : type === 'month' ? 'month' : 'year').valueOf(),
    queryType: type
  }

  getWaterSupplyAndEnergyData(params).then(res => {
    const data = res.data.data || []

    // 过滤出选中站点的数据
    const filteredData = Array.isArray(data) ? data.filter((item: any) => item.stationId === state.stationId) : []

    // 模拟泵组运行状态数据
    const pumpStatusData = generatePumpStatusData(filteredData, stationName)

    cardTableConfig.dataList = pumpStatusData
    cardTableConfig.loading = false

    // 刷新图表
    refreshChart(pumpStatusData)
  }).catch(err => {
    console.error('查询运行状态数据失败:', err)
    cardTableConfig.loading = false
  })
}

// 生成泵组运行状态数据
const generatePumpStatusData = (data: any[], stationName: string) => {
  // 模拟多个泵组数据
  const pumpGroups = [
    '清水泵1#', '清水泵2#', '清水泵3#', '清水泵4#', '清水泵5#',
    '清水泵6#', '清水泵7#', '清水泵8#', '清水泵9#', '清水泵10#'
  ]

  return pumpGroups.map((pumpName, index) => {
    const yesterdayRunTime = Math.round(18 + Math.random() * 6) // 18-24小时
    const todayRunTime = Math.round(16 + Math.random() * 8) // 16-24小时
    const stopTime = 24 - todayRunTime // 停机时长

    return {
      pumpName: `${stationName}${pumpName}`,
      yesterdayRunTime: yesterdayRunTime.toFixed(1),
      todayRunTime: todayRunTime.toFixed(1),
      stopTime: stopTime.toFixed(1)
    }
  })
}

// 刷新图表
const refreshChart = (data: any[]) => {
  if (!refChart.value || !data.length) {
    return
  }

  try {
    refChart.value.clear()
    nextTick(() => {
      if (!refChart.value) return

      const chartOption = horizontalBarOption()

      // 设置Y轴数据（泵组名称）
      chartOption.yAxis.data = data.map(item => item.pumpName)

      // 设置系列数据
      chartOption.series = [
        {
          name: '昨日运行',
          type: 'bar',
          data: data.map(item => parseFloat(item.yesterdayRunTime)),
          itemStyle: {
            color: '#5470C6'
          },
          barHeight: 20
        },
        {
          name: '今日运行',
          type: 'bar',
          data: data.map(item => parseFloat(item.todayRunTime)),
          itemStyle: {
            color: '#91CC75'
          },
          barHeight: 20
        },
        {
          name: '停机时长',
          type: 'bar',
          data: data.map(item => parseFloat(item.stopTime)),
          itemStyle: {
            color: '#FAC858'
          },
          barHeight: 20
        }
      ]

      state.chartOption = chartOption
      refChart.value?.setOption(chartOption)
    })
  } catch (error) {
    console.error('图表渲染错误:', error)
  }
}

onMounted(async () => {
  // 初始化站点树 - 使用泵站类型
  const treeData = await getStationTree('泵站')
  TreeData.data = treeData
  const firstStation = getFormatTreeNodeDeepestChild(treeData)
  if (firstStation && firstStation.id) {
    TreeData.currentProject = firstStation
    state.treeDataType = firstStation.data?.type || 'Station'
    state.stationId = firstStation.id
  }
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()

  // 初始化数据
  if (state.stationId) {
    refreshData()
  }
})

// 导出运行状态报告
const _exportOperationStatus = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 图表大小调整
const resizeChart = () => {
  refChart.value?.resize()
}

onUnmounted(() => {
  // 清理资源
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.content-container {
  display: flex;
  gap: 16px;
  flex: 1;
  min-height: 0;
}

.left-panel {
  width: 50%;
  min-width: 400px;
}

.right-panel {
  flex: 1;
  min-width: 0;
}

.chart-container {
  height: 600px;
  width: 100%;
}

.card-table {
  height: 100%;
}

:deep(.el-card__body) {
  height: calc(100% - 60px);
  padding: 16px;
}
</style>
